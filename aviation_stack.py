import os
import urllib.request
import urllib.parse
import json
import logging
import ssl
from typing import Optional, Dict, Any, List
from datetime import datetime
from mcp.server.fastmcp import FastMCP

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create an MCP server for Aviationstack API
mcp = FastMCP("Aviationstack")

# Base URL for the API
BASE_URL = "https://api.aviationstack.com/v1"

def get_api_key() -> str:
    """Get API key from environment variable"""
    # First try environment variable, then fallback to hardcoded (not recommended for production)
    api_key = os.getenv("AVIATIONSTACK_API_KEY", "********************************")
    if not api_key:
        raise ValueError("AVIATIONSTACK_API_KEY environment variable is required")
    return api_key

def make_request(endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]:
    """Make a request to the Aviationstack API using urllib (no external dependencies)"""
    try:
        params["access_key"] = get_api_key()
        params["limit"] = 20
        logger.info(f"Making request to {endpoint} with params: {params}")
        
        # Build query string
        query_string = urllib.parse.urlencode(params)
        url = f"{BASE_URL}/{endpoint}?{query_string}"
        logger.info(f"Request URL: {url}")
        
        # Create SSL context that doesn't verify certificates (for development only)
        # For production, you should fix the certificate issue properly
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE
        
        # Make request with SSL context
        with urllib.request.urlopen(url, timeout=30, context=ssl_context) as response:
            data = response.read()
            result = json.loads(data.decode('utf-8'))
            logger.info(f"API response received: {len(result.get('data', []))} records")
            return result
            
    except Exception as e:
        logger.error(f"API request failed: {e}")
        return {
            "error": str(e),
            "pagination": {"limit": 100, "offset": 0, "count": 0, "total": 0},
            "data": []
        }

# ============================================================================
# FLIGHTS ENDPOINTS
# ============================================================================

@mcp.tool()
def get_real_time_flights(
    limit: Optional[int] = 20,
    offset: Optional[int] = 0,
    flight_status: Optional[str] = None,
    dep_iata: Optional[str] = None,
    arr_iata: Optional[str] = None,
    dep_icao: Optional[str] = None,
    arr_icao: Optional[str] = None,
    airline_name: Optional[str] = None,
    airline_iata: Optional[str] = None,
    airline_icao: Optional[str] = None,
    flight_number: Optional[str] = None,
    flight_iata: Optional[str] = None,
    flight_icao: Optional[str] = None,
    min_delay_dep: Optional[int] = None,
    min_delay_arr: Optional[int] = None,
    max_delay_dep: Optional[int] = None,
    max_delay_arr: Optional[int] = None
) -> Dict[str, Any]:
    """
    Get real-time flight information.
    
    Args:
        limit: Limit of results (max 100 for basic plans, 1000 for professional+)
        offset: Pagination offset
        flight_status: Filter by status (scheduled, active, landed, cancelled, incident, diverted)
        dep_iata: Departure airport IATA code
        arr_iata: Arrival airport IATA code
        dep_icao: Departure airport ICAO code
        arr_icao: Arrival airport ICAO code
        airline_name: Airline name
        airline_iata: Airline IATA code
        airline_icao: Airline ICAO code
        flight_number: Flight number
        flight_iata: Flight IATA code
        flight_icao: Flight ICAO code
        min_delay_dep: Minimum departure delay in minutes
        min_delay_arr: Minimum arrival delay in minutes
        max_delay_dep: Maximum departure delay in minutes
        max_delay_arr: Maximum arrival delay in minutes
    """
    params = {k: v for k, v in locals().items() if v is not None and k != 'self'}
    return make_request("flights", params)

@mcp.tool()
def get_historical_flights(
    flight_date: str,
    limit: Optional[int] = 20,
    offset: Optional[int] = 0,
    flight_status: Optional[str] = None,
    dep_iata: Optional[str] = None,
    arr_iata: Optional[str] = None,
    dep_icao: Optional[str] = None,
    arr_icao: Optional[str] = None,
    airline_name: Optional[str] = None,
    airline_iata: Optional[str] = None,
    airline_icao: Optional[str] = None,
    flight_number: Optional[str] = None,
    flight_iata: Optional[str] = None,
    flight_icao: Optional[str] = None,
    min_delay_dep: Optional[int] = None,
    min_delay_arr: Optional[int] = None,
    max_delay_dep: Optional[int] = None,
    max_delay_arr: Optional[int] = None
) -> Dict[str, Any]:
    """
    Get historical flight information for a specific date (last 3 months only).
    
    Args:
        flight_date: Flight date in YYYY-MM-DD format (required)
        limit: Limit of results
        offset: Pagination offset
        flight_status: Filter by status
        dep_iata: Departure airport IATA code
        arr_iata: Arrival airport IATA code
        dep_icao: Departure airport ICAO code
        arr_icao: Arrival airport ICAO code
        airline_name: Airline name
        airline_iata: Airline IATA code
        airline_icao: Airline ICAO code
        flight_number: Flight number
        flight_iata: Flight IATA code
        flight_icao: Flight ICAO code
        min_delay_dep: Minimum departure delay in minutes
        min_delay_arr: Minimum arrival delay in minutes
        max_delay_dep: Maximum departure delay in minutes
        max_delay_arr: Maximum arrival delay in minutes
    """
    params = {k: v for k, v in locals().items() if v is not None and k != 'self'}
    return make_request("flights", params)

# ============================================================================
# ROUTES ENDPOINT
# ============================================================================

@mcp.tool()
def get_airline_routes(
    limit: Optional[int] = 20,
    offset: Optional[int] = 0,
    flight_number: Optional[str] = None,
    dep_iata: Optional[str] = None,
    arr_iata: Optional[str] = None,
    dep_icao: Optional[str] = None,
    arr_icao: Optional[str] = None,
    airline_iata: Optional[str] = None,
    airline_icao: Optional[str] = None
) -> Dict[str, Any]:
    """
    Get airline route information (updated every 24 hours).
    
    Args:
        limit: Limit of results
        offset: Pagination offset
        flight_number: Flight number
        dep_iata: Departure airport IATA code
        arr_iata: Arrival airport IATA code
        dep_icao: Departure airport ICAO code
        arr_icao: Arrival airport ICAO code
        airline_iata: Airline IATA code
        airline_icao: Airline ICAO code
    """
    params = {k: v for k, v in locals().items() if v is not None and k != 'self'}
    return make_request("routes", params)

# ============================================================================
# AIRPORTS ENDPOINT
# ============================================================================

@mcp.tool()
def get_airports(
    limit: Optional[int] = 20,
    offset: Optional[int] = 0,
    search: Optional[str] = None
) -> Dict[str, Any]:
    """
    Get airport information with autocomplete search capability.
    
    Args:
        limit: Limit of results
        offset: Pagination offset
        search: Search term for autocomplete suggestions (Basic Plan and higher)
    """
    params = {k: v for k, v in locals().items() if v is not None and k != 'self'}
    return make_request("airports", params)

# ============================================================================
# AIRLINES ENDPOINT
# ============================================================================

@mcp.tool()
def get_airlines(
    limit: Optional[int] = 20,
    offset: Optional[int] = 0,
    search: Optional[str] = None
) -> Dict[str, Any]:
    """
    Get airline information with autocomplete search capability.
    
    Args:
        limit: Limit of results
        offset: Pagination offset
        search: Search term for autocomplete suggestions
    """
    params = {k: v for k, v in locals().items() if v is not None and k != 'self'}
    return make_request("airlines", params)

# ============================================================================
# AIRPLANES ENDPOINT
# ============================================================================

@mcp.tool()
def get_airplanes(
    limit: Optional[int] = 20,
    offset: Optional[int] = 0,
    search: Optional[str] = None
) -> Dict[str, Any]:
    """
    Get airplane/aircraft information with autocomplete search capability.
    
    Args:
        limit: Limit of results
        offset: Pagination offset
        search: Search term for autocomplete suggestions
    """
    params = {k: v for k, v in locals().items() if v is not None and k != 'self'}
    return make_request("airplanes", params)

# ============================================================================
# AIRCRAFT TYPES ENDPOINT
# ============================================================================

@mcp.tool()
def get_aircraft_types(
    limit: Optional[int] = 20,
    offset: Optional[int] = 0,
    search: Optional[str] = None
) -> Dict[str, Any]:
    """
    Get aircraft type information with autocomplete search capability.
    
    Args:
        limit: Limit of results
        offset: Pagination offset
        search: Search term for autocomplete suggestions
    """
    params = {k: v for k, v in locals().items() if v is not None and k != 'self'}
    return make_request("aircraft_types", params)

# ============================================================================
# AVIATION TAXES ENDPOINT
# ============================================================================

@mcp.tool()
def get_aviation_taxes(
    limit: Optional[int] = 20,
    offset: Optional[int] = 0,
    search: Optional[str] = None
) -> Dict[str, Any]:
    """
    Get aviation tax information with autocomplete search capability.
    
    Args:
        limit: Limit of results
        offset: Pagination offset
        search: Search term for autocomplete suggestions
    """
    params = {k: v for k, v in locals().items() if v is not None and k != 'self'}
    return make_request("taxes", params)

# ============================================================================
# CITIES ENDPOINT
# ============================================================================

@mcp.tool()
def get_cities(
    limit: Optional[int] = 20,
    offset: Optional[int] = 0,
    search: Optional[str] = None
) -> Dict[str, Any]:
    """
    Get city information with autocomplete search capability.
    
    Args:
        limit: Limit of results
        offset: Pagination offset
        search: Search term for autocomplete suggestions
    """
    params = {k: v for k, v in locals().items() if v is not None and k != 'self'}
    return make_request("cities", params)

# ============================================================================
# COUNTRIES ENDPOINT
# ============================================================================

@mcp.tool()
def get_countries(
    limit: Optional[int] = 20,
    offset: Optional[int] = 0,
    search: Optional[str] = None
) -> Dict[str, Any]:
    """
    Get country information with autocomplete search capability.
    
    Args:
        limit: Limit of results
        offset: Pagination offset
        search: Search term for autocomplete suggestions
    """
    params = {k: v for k, v in locals().items() if v is not None and k != 'self'}
    return make_request("countries", params)

# ============================================================================
# FLIGHT SCHEDULES ENDPOINT (Basic Plan and higher)
# ============================================================================

@mcp.tool()
def get_flight_schedules(
    iata_code: str,
    schedule_type: str,
    status: Optional[str] = None,
    dep_terminal: Optional[str] = None,
    dep_delay: Optional[int] = None,
    dep_sch_time: Optional[str] = None,
    dep_est_time: Optional[str] = None,
    dep_act_time: Optional[str] = None,
    dep_est_runway: Optional[str] = None,
    dep_act_runway: Optional[str] = None,
    arr_terminal: Optional[str] = None,
    arr_delay: Optional[int] = None,
    arr_sch_time: Optional[str] = None,
    arr_est_time: Optional[str] = None,
    arr_act_time: Optional[str] = None,
    arr_est_runway: Optional[str] = None,
    arr_act_runway: Optional[str] = None,
    airline_name: Optional[str] = None,
    airline_iata: Optional[str] = None,
    airline_icao: Optional[str] = None,
    flight_num: Optional[str] = None,
    flight_iata: Optional[str] = None,
    flight_icao: Optional[str] = None,
    lang: Optional[str] = None
) -> Dict[str, Any]:
    """
    Get real-time timetable information for flights on the current day.
    Rate limited to 1 call per minute.
    
    Args:
        iata_code: Airport IATA code (required)
        schedule_type: Type of schedule - 'departure' or 'arrival' (required)
        status: Flight status (landed, scheduled, cancelled, active, incident, diverted, redirected, unknown)
        dep_terminal: Departure terminal
        dep_delay: Departure delay in minutes
        dep_sch_time: Scheduled departure time (YYYY-MM-DDTHH:MM:SS.sss format)
        dep_est_time: Estimated departure time
        dep_act_time: Actual departure time
        dep_est_runway: Estimated departure runway time
        dep_act_runway: Actual departure runway time
        arr_terminal: Arrival terminal
        arr_delay: Arrival delay in minutes
        arr_sch_time: Scheduled arrival time
        arr_est_time: Estimated arrival time
        arr_act_time: Actual arrival time
        arr_est_runway: Estimated arrival runway time
        arr_act_runway: Actual arrival runway time
        airline_name: Airline name
        airline_iata: Airline IATA code
        airline_icao: Airline ICAO code
        flight_num: Flight number
        flight_iata: Flight IATA code
        flight_icao: Flight ICAO code
        lang: Language code for translations (43 languages available)
    """
    params = {"iataCode": iata_code, "type": schedule_type}
    
    # Add optional parameters
    optional_params = {
        k: v for k, v in locals().items() 
        if v is not None and k not in ['self', 'iata_code', 'schedule_type']
    }
    params.update(optional_params)
    
    return make_request("timetable", params)

# ============================================================================
# FLIGHT FUTURE SCHEDULES ENDPOINT (Basic Plan and higher)
# ============================================================================

@mcp.tool()
def get_flight_future_schedules(
    iata_code: str,
    schedule_type: str,
    date: str,
    airline_iata: Optional[str] = None,
    airline_icao: Optional[str] = None,
    flight_number: Optional[str] = None
) -> Dict[str, Any]:
    """
    Get future flight schedule information.
    Rate limited to 1 call per minute.
    
    Args:
        iata_code: Airport IATA code (required)
        schedule_type: Type of schedule - 'departure' or 'arrival' (required)
        date: Future date in YYYY-MM-DD format (required)
        airline_iata: Airline IATA code filter
        airline_icao: Airline ICAO code filter
        flight_number: Flight number filter
    """
    params = {
        "iataCode": iata_code,
        "type": schedule_type,
        "date": date
    }
    
    # Add optional filters
    if airline_iata:
        params["airline_iata"] = airline_iata
    if airline_icao:
        params["airline_icao"] = airline_icao
    if flight_number:
        params["flight_number"] = flight_number
    
    return make_request("flightsFuture", params)

# ============================================================================
# CONVENIENCE FUNCTIONS
# ============================================================================

@mcp.tool()
def search_flights_by_route(
    departure_iata: str,
    arrival_iata: str,
    flight_date: Optional[str] = None,
    airline_iata: Optional[str] = None,
    flight_status: Optional[str] = None
) -> Dict[str, Any]:
    """
    Search for flights between two airports.
    
    Args:
        departure_iata: Departure airport IATA code
        arrival_iata: Arrival airport IATA code
        flight_date: Optional date in YYYY-MM-DD format (for historical data)
        airline_iata: Optional airline filter
        flight_status: Optional status filter
    """
    params = {
        "dep_iata": departure_iata,
        "arr_iata": arrival_iata
    }
    
    if flight_date:
        params["flight_date"] = flight_date
    if airline_iata:
        params["airline_iata"] = airline_iata
    if flight_status:
        params["flight_status"] = flight_status
    
    return make_request("flights", params)

@mcp.tool()
def get_airline_fleet_info(airline_iata: str) -> Dict[str, Any]:
    """
    Get detailed airline information including fleet data.
    
    Args:
        airline_iata: Airline IATA code
    """
    return make_request("airlines", {"search": airline_iata, "limit": 10})

@mcp.tool()
def get_airport_details(airport_iata: str) -> Dict[str, Any]:
    """
    Get detailed airport information.
    
    Args:
        airport_iata: Airport IATA code
    """
    return make_request("airports", {"search": airport_iata, "limit": 10})

@mcp.tool()
def get_delayed_flights(
    min_delay_minutes: int = 30,
    airport_iata: Optional[str] = None,
    flight_status: str = "active"
) -> Dict[str, Any]:
    """
    Get flights with delays above a threshold.
    
    Args:
        min_delay_minutes: Minimum delay in minutes
        airport_iata: Optional airport filter (departure)
        flight_status: Flight status filter
    """
    params = {
        "min_delay_dep": min_delay_minutes,
        "flight_status": flight_status
    }
    
    if airport_iata:
        params["dep_iata"] = airport_iata
    
    return make_request("flights", params)

# ============================================================================
# RESOURCES
# ============================================================================

@mcp.resource("airport://{iata_code}")
def get_airport_resource(iata_code: str) -> str:
    """Get airport information by IATA code"""
    try:
        logger.info(f"Getting airport resource for {iata_code}")
        result = make_request("airports", {"search": iata_code, "limit": 1})
        
        if "error" in result:
            return f"Error: {result['error']}"
            
        if result.get("data") and len(result["data"]) > 0:
            airport = result["data"][0]
            return f"Airport: {airport.get('airport_name', 'Unknown')} ({airport.get('iata_code', 'N/A')}) in {airport.get('city_iata_code', 'Unknown')}, {airport.get('country_name', 'Unknown')}"
        return f"Airport {iata_code} not found"
    except Exception as e:
        logger.error(f"Error in airport resource: {e}")
        return f"Error fetching airport info: {str(e)}"

@mcp.resource("airline://{iata_code}")
def get_airline_resource(iata_code: str) -> str:
    """Get airline information by IATA code"""
    try:
        logger.info(f"Getting airline resource for {iata_code}")
        result = make_request("airlines", {"search": iata_code, "limit": 1})
        
        if "error" in result:
            return f"Error: {result['error']}"
            
        if result.get("data") and len(result["data"]) > 0:
            airline = result["data"][0]
            return f"Airline: {airline.get('airline_name', 'Unknown')} ({airline.get('iata_code', 'N/A')}) - Fleet size: {airline.get('fleet_size', 'Unknown')}, Founded: {airline.get('date_founded', 'Unknown')}"
        return f"Airline {iata_code} not found"
    except Exception as e:
        logger.error(f"Error in airline resource: {e}")
        return f"Error fetching airline info: {str(e)}"

@mcp.resource("flight://{flight_iata}")
def get_flight_resource(flight_iata: str) -> str:
    """Get current flight information by IATA flight code"""
    try:
        logger.info(f"Getting flight resource for {flight_iata}")
        result = make_request("flights", {"flight_iata": flight_iata, "limit": 1})
        
        if "error" in result:
            return f"Error: {result['error']}"
            
        if result.get("data") and len(result["data"]) > 0:
            flight = result["data"][0]
            dep = flight.get("departure", {})
            arr = flight.get("arrival", {})
            airline = flight.get("airline", {})
            return f"Flight {flight_iata} ({airline.get('name', 'Unknown')}): {dep.get('iata', 'UNK')} → {arr.get('iata', 'UNK')}, Status: {flight.get('flight_status', 'Unknown')}"
        return f"Flight {flight_iata} not found"
    except Exception as e:
        logger.error(f"Error in flight resource: {e}")
        return f"Error fetching flight info: {str(e)}"

@mcp.resource("route://{dep_iata}/{arr_iata}")
def get_route_resource(dep_iata: str, arr_iata: str) -> str:
    """Get route information between two airports"""
    try:
        logger.info(f"Getting route resource for {dep_iata} to {arr_iata}")
        result = make_request("routes", {"dep_iata": dep_iata, "arr_iata": arr_iata, "limit": 5})
        
        if "error" in result:
            return f"Error: {result['error']}"
            
        if result.get("data") and len(result["data"]) > 0:
            routes = result["data"]
            route_info = []
            for route in routes[:3]:  # Show first 3 routes
                airline = route.get("airline", {})
                flight = route.get("flight", {})
                dep_time = route.get("departure", {}).get("time", "Unknown")
                arr_time = route.get("arrival", {}).get("time", "Unknown")
                route_info.append(f"{airline.get('name', 'Unknown')} {flight.get('number', 'N/A')}: {dep_time} → {arr_time}")
            return f"Routes {dep_iata} → {arr_iata}:\n" + "\n".join(route_info)
        return f"No routes found between {dep_iata} and {arr_iata}"
    except Exception as e:
        logger.error(f"Error in route resource: {e}")
        return f"Error fetching route info: {str(e)}"

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

def test_api_connection():
    """Test the API connection with a simple request"""
    try:
        logger.info("Testing API connection...")
        result = make_request("flights", {"limit": 1})
        if "error" in result:
            logger.error(f"API test failed: {result['error']}")
            return False
        else:
            logger.info("API test successful!")
            return True
    except Exception as e:
        logger.error(f"API test exception: {e}")
        return False

# ============================================================================
# MAIN EXECUTION
# ============================================================================

if __name__ == "__main__":
    mcp.run()