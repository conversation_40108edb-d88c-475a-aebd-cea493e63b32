# MCP Server Demo - Ovation CXM

A comprehensive **Model Context Protocol (MCP) server demonstration** that simulates a customer service management system called "Ovation CXM". This project showcases how AI assistants can interact with customer service data through MCP tools to provide intelligent analysis and insights.

## 🎯 Project Overview

This demo consists of two main components:

1. **Mock API Server** (`api_server.py`) - A FastAPI-based server that simulates a realistic customer service management system with endpoints for issues, customers, billing, and analytics
2. **MCP Server** (`ovationcxm.py`) - An MCP server that provides AI tools for analyzing customer service data, identifying churn risks, and generating executive summaries

The system uses realistic demo data including enterprise customers, support tickets, chat histories, and escalation patterns to demonstrate how AI can enhance customer service operations.

## 📋 Prerequisites

Before setting up this project, ensure you have the following installed:

- **Python 3.13+** (required by the project configuration)
- **uv** - Modern Python package manager (recommended for dependency management)
- **Git** (for cloning the repository)

### Installing uv (Python Package Manager)

`uv` is a fast Python package manager that handles virtual environments and dependencies efficiently.

#### macOS/Linux:
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

#### Windows:
```powershell
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
```

#### Alternative installation methods:
```bash
# Using pip
pip install uv

# Using Homebrew (macOS)
brew install uv

# Using pipx
pipx install uv
```

Verify installation:
```bash
uv --version
```

## 🚀 Setup Instructions

### 1. Clone the Repository
```bash
git clone <repository-url>
cd mcp-server-demo
```

### 2. Set Up Python Environment
Using `uv` (recommended):
```bash
# Create and activate virtual environment with Python 3.13+
uv venv

# Activate the virtual environment
# On macOS/Linux:
source .venv/bin/activate
# On Windows:
.venv\Scripts\activate
```

### 3. Install Dependencies
```bash
# Install all project dependencies
uv pip install -e .

# Or install from requirements if available
uv pip install fastapi mcp[cli] uvicorn httpx
```

### 4. Verify Installation
```bash
# Check that all dependencies are installed
uv pip list

# Test basic functionality
python main.py
```

## 🔧 Usage Guide

### Running the Mock API Server

The `api_server.py` file provides a comprehensive REST API that simulates a customer service management system.

**What it does:**
- Serves realistic customer service data (issues, customers, agents, chat history)
- Provides endpoints for querying issues, customer health, and performance metrics
- Includes special demo endpoints for weekend escalations and churn analysis
- Supports filtering and searching across different data types

**To start the API server:**
```bash
python api_server.py
```

The server will start on `http://localhost:8000` with the following key endpoints:

#### Core API Endpoints:
- `GET /` - API information and status
- `GET /issues/get` - List all issues (with optional filtering)
- `GET /issues/get/{issue_id}` - Get specific issue details
- `GET /issues/chat/history/{issue_id}` - Get chat history for an issue
- `GET /issues/status_history/{issue_id}` - Get status change history

#### Demo Analytics Endpoints:
- `GET /demo/weekend_escalations` - Analyze weekend escalations
- `GET /demo/customer_health/{customer_id}` - Customer health analysis
- `GET /demo/performance_metrics` - Overall performance metrics

#### Interactive API Documentation:
- Visit `http://localhost:8000/docs` for Swagger UI documentation
- Visit `http://localhost:8000/redoc` for ReDoc documentation

### Running the MCP Server

The `ovationcxm.py` file implements an MCP server that provides AI tools for customer service analysis.

**What it does:**
- Connects to the mock API server to fetch customer service data
- Provides intelligent analysis tools for AI assistants
- Generates prioritized action plans and executive summaries
- Identifies churn risks and customer patterns

**Available MCP Tools:**
1. `analyze_weekend_escalations()` - Analyzes weekend escalations and provides prioritized Monday morning action plan
2. `get_customer_full_context(customer)` - Gets complete customer context including issue history and risk assessment
3. `get_issue_context(issue_id)` - Gets complete context for a specific issue with recommendations
4. `generate_executive_summary()` - Generates executive dashboard with performance metrics
5. `analyze_churn_risk()` - Identifies customers at risk of churning

**To start the MCP server:**
```bash
python ovationcxm.py
```

The MCP server will connect to the API server (default: `http://localhost:8000`) and be ready to receive MCP requests.

## ⚙️ Configuration

### Environment Variables

You can configure the system using the following environment variables:

```bash
# API Server Configuration
export OVATION_API_URL="http://localhost:8000"  # Default API server URL

# For production or different environments
export OVATION_API_URL="https://your-api-server.com"
```

### Configuration Files

- **`pyproject.toml`** - Project metadata and dependencies
- **`demo_data.json`** - Comprehensive demo data including customers, issues, chat history, and analytics
- **`uv.lock`** - Locked dependency versions (managed by uv)

## 📚 Examples

### Basic Usage Examples

#### 1. Start Both Servers
```bash
# Terminal 1: Start the API server
python api_server.py

# Terminal 2: Start the MCP server (in a new terminal)
python ovationcxm.py
```

#### 2. Test API Endpoints
```bash
# Get all issues
curl http://localhost:8000/issues/get

# Get weekend escalations
curl http://localhost:8000/demo/weekend_escalations

# Get customer health for TechCorp
curl http://localhost:8000/demo/customer_health/CUST-12345

# Get performance metrics
curl http://localhost:8000/demo/performance_metrics
```

#### 3. Sample MCP Tool Usage

When connected to an AI assistant that supports MCP, you can use commands like:

- "Analyze the weekend escalations and give me a priority list for Monday"
- "Get the full context for customer John Smith"
- "What's the current status of issue ISS-2024-089?"
- "Generate an executive summary of our customer service performance"
- "Which customers are at risk of churning?"

### Sample Data Overview

The demo includes realistic data for:

- **4 Customers**: 3 enterprise customers (TechCorp, DataFlow, CloudFirst) + 1 individual (John Smith)
- **9 Issues**: Mix of technical, billing, and account issues with various priorities
- **3 Support Agents**: Marcus Johnson, Lisa Chen, David Rodriguez
- **Chat History**: Customer communications with sentiment analysis
- **Status Changes**: Issue lifecycle tracking
- **Churn Risk Data**: Customer health indicators and risk factors

## 🔍 Troubleshooting

### Common Issues and Solutions

#### 1. **Port Already in Use**
```
Error: [Errno 48] Address already in use
```
**Solution:**
```bash
# Find process using port 8000
lsof -i :8000

# Kill the process
kill -9 <PID>

# Or use a different port
uvicorn api_server:app --port 8001
```

#### 2. **Module Not Found Errors**
```
ModuleNotFoundError: No module named 'fastapi'
```
**Solution:**
```bash
# Ensure virtual environment is activated
source .venv/bin/activate  # macOS/Linux
# or
.venv\Scripts\activate     # Windows

# Reinstall dependencies
uv pip install -e .
```

#### 3. **MCP Server Connection Issues**
```
Error: Connection refused to http://localhost:8000
```
**Solution:**
- Ensure the API server is running first (`python api_server.py`)
- Check that the API server is accessible at `http://localhost:8000`
- Verify the `OVATION_API_URL` environment variable if using a custom URL

#### 4. **Python Version Issues**
```
Error: This project requires Python >=3.13
```
**Solution:**
```bash
# Check Python version
python --version

# Install Python 3.13+ or use uv to manage Python versions
uv python install 3.13
uv venv --python 3.13
```

#### 5. **Demo Data Not Loading**
```
FileNotFoundError: [Errno 2] No such file or directory: 'demo_data.json'
```
**Solution:**
- Ensure you're running the API server from the project root directory
- Verify that `demo_data.json` exists in the same directory as `api_server.py`

#### 6. **CORS Issues (Browser Access)**
If accessing the API from a web browser:
- The API server includes CORS middleware configured to allow all origins
- For production, configure specific allowed origins in `api_server.py`

### Getting Help

If you encounter issues not covered here:

1. **Check the logs** - Both servers provide detailed logging output
2. **Verify dependencies** - Run `uv pip list` to ensure all packages are installed
3. **Test API connectivity** - Use `curl` commands to test API endpoints
4. **Check Python version** - Ensure you're using Python 3.13+

### Development Tips

- **Hot Reload**: The API server runs with `reload=True` for development
- **API Documentation**: Always available at `http://localhost:8000/docs`
- **Data Modification**: Edit `demo_data.json` to customize the demo data
- **Adding Tools**: Extend the MCP server by adding new `@mcp.tool()` functions

---

## 🤝 Contributing

This is a demonstration project. Feel free to:
- Extend the demo data in `demo_data.json`
- Add new MCP tools in `ovationcxm.py`
- Enhance the API endpoints in `api_server.py`
- Improve the documentation

## 📄 License

This project is provided as-is for demonstration purposes.


### Serve using 
```bash
uvicorn api_server:app --reload
```